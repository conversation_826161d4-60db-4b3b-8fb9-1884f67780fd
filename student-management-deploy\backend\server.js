const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const database = require('./src/models/database');
const studentRoutes = require('./src/routes/students');

const app = express();
const PORT = process.env.PORT || 3000;
const NODE_ENV = process.env.NODE_ENV || 'development';

// 中间件配置
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// 请求日志中间件
app.use((req, res, next) => {
  if (NODE_ENV === 'development') {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  }
  next();
});

// API路由
app.use('/api/students', studentRoutes);

// 根路径
app.get('/', (req, res) => {
  res.json({
    message: '学生管理系统API服务',
    version: '1.0.0',
    endpoints: {
      students: {
        'GET /api/students': '获取所有学生',
        'GET /api/students/:id': '获取指定学生',
        'POST /api/students': '添加学生',
        'PUT /api/students/:id': '更新学生信息',
        'DELETE /api/students/:id': '删除学生'
      }
    }
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在'
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? err.message : '服务器错误'
  });
});

// 启动服务器
async function startServer() {
  try {
    console.log('正在初始化数据库...');
    // 初始化数据库
    await database.init();
    console.log('数据库初始化完成');

    // 启动HTTP服务器
    console.log(`正在启动服务器，端口: ${PORT}`);
    app.listen(PORT, '0.0.0.0', () => {
      console.log(`服务器运行在 http://localhost:${PORT}`);
      console.log(`API文档: http://localhost:${PORT}`);
      console.log(`环境: ${NODE_ENV}`);
    });
  } catch (error) {
    console.error('服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n正在关闭服务器...');
  database.close();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n正在关闭服务器...');
  database.close();
  process.exit(0);
});

startServer();
