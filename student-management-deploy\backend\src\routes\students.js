const express = require('express');
const router = express.Router();
const studentController = require('../controllers/studentController');

// 获取所有学生
router.get('/', studentController.getAllStudents);

// 根据ID获取学生
router.get('/:id', studentController.getStudentById);

// 添加学生
router.post('/', studentController.addStudent);

// 更新学生信息
router.put('/:id', studentController.updateStudent);

// 删除学生
router.delete('/:id', studentController.deleteStudent);

module.exports = router;
