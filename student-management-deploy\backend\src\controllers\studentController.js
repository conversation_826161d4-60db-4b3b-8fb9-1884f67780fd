const database = require('../models/database');

class StudentController {
  // 获取所有学生
  async getAllStudents(req, res) {
    try {
      const students = await database.getAllStudents();
      res.json({
        success: true,
        data: students,
        message: '获取学生列表成功'
      });
    } catch (error) {
      console.error('获取学生列表失败:', error);
      res.status(500).json({
        success: false,
        message: '获取学生列表失败',
        error: error.message
      });
    }
  }

  // 根据ID获取学生
  async getStudentById(req, res) {
    try {
      const { id } = req.params;
      const student = await database.getStudentById(id);
      
      if (!student) {
        return res.status(404).json({
          success: false,
          message: '学生不存在'
        });
      }

      res.json({
        success: true,
        data: student,
        message: '获取学生信息成功'
      });
    } catch (error) {
      console.error('获取学生信息失败:', error);
      res.status(500).json({
        success: false,
        message: '获取学生信息失败',
        error: error.message
      });
    }
  }

  // 添加学生
  async addStudent(req, res) {
    try {
      const { name, studentId, class: className, phone, email } = req.body;

      // 验证必填字段
      if (!name || !studentId || !className) {
        return res.status(400).json({
          success: false,
          message: '姓名、学号和班级为必填字段'
        });
      }

      const studentData = {
        name,
        studentId,
        class: className,
        phone: phone || '',
        email: email || ''
      };

      const newStudent = await database.addStudent(studentData);
      
      res.status(201).json({
        success: true,
        data: newStudent,
        message: '添加学生成功'
      });
    } catch (error) {
      console.error('添加学生失败:', error);
      
      // 处理学号重复的情况
      if (error.message.includes('UNIQUE constraint failed')) {
        return res.status(400).json({
          success: false,
          message: '学号已存在，请使用不同的学号'
        });
      }

      res.status(500).json({
        success: false,
        message: '添加学生失败',
        error: error.message
      });
    }
  }

  // 更新学生信息
  async updateStudent(req, res) {
    try {
      const { id } = req.params;
      const { name, studentId, class: className, phone, email } = req.body;

      // 验证必填字段
      if (!name || !studentId || !className) {
        return res.status(400).json({
          success: false,
          message: '姓名、学号和班级为必填字段'
        });
      }

      const studentData = {
        name,
        studentId,
        class: className,
        phone: phone || '',
        email: email || ''
      };

      const updatedStudent = await database.updateStudent(id, studentData);
      
      res.json({
        success: true,
        data: updatedStudent,
        message: '更新学生信息成功'
      });
    } catch (error) {
      console.error('更新学生信息失败:', error);
      
      if (error.message === '学生不存在') {
        return res.status(404).json({
          success: false,
          message: '学生不存在'
        });
      }

      // 处理学号重复的情况
      if (error.message.includes('UNIQUE constraint failed')) {
        return res.status(400).json({
          success: false,
          message: '学号已存在，请使用不同的学号'
        });
      }

      res.status(500).json({
        success: false,
        message: '更新学生信息失败',
        error: error.message
      });
    }
  }

  // 删除学生
  async deleteStudent(req, res) {
    try {
      const { id } = req.params;
      await database.deleteStudent(id);
      
      res.json({
        success: true,
        message: '删除学生成功'
      });
    } catch (error) {
      console.error('删除学生失败:', error);
      
      if (error.message === '学生不存在') {
        return res.status(404).json({
          success: false,
          message: '学生不存在'
        });
      }

      res.status(500).json({
        success: false,
        message: '删除学生失败',
        error: error.message
      });
    }
  }
}

module.exports = new StudentController();
