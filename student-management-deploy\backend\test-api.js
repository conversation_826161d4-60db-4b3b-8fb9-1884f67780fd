// 简单的API测试脚本
const http = require('http');

// 测试添加学生
function testAddStudent() {
  const postData = JSON.stringify({
    name: '张三',
    studentId: 'S001',
    class: '计算机1班',
    phone: '13800138000',
    email: '<PERSON><PERSON><PERSON>@example.com'
  });

  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/students',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  const req = http.request(options, (res) => {
    console.log(`状态码: ${res.statusCode}`);
    console.log(`响应头: ${JSON.stringify(res.headers)}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('添加学生响应:', data);
      testGetStudents();
    });
  });

  req.on('error', (e) => {
    console.error(`请求遇到问题: ${e.message}`);
  });

  req.write(postData);
  req.end();
}

// 测试获取学生列表
function testGetStudents() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/students',
    method: 'GET'
  };

  const req = http.request(options, (res) => {
    console.log(`状态码: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('获取学生列表响应:', data);
    });
  });

  req.on('error', (e) => {
    console.error(`请求遇到问题: ${e.message}`);
  });

  req.end();
}

// 测试服务器连接
function testConnection() {
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/',
    method: 'GET'
  };

  const req = http.request(options, (res) => {
    console.log(`服务器连接成功，状态码: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('服务器响应:', data);
      console.log('\n开始测试API...\n');
      testAddStudent();
    });
  });

  req.on('error', (e) => {
    console.error(`服务器连接失败: ${e.message}`);
    console.log('请确保服务器正在运行在端口3000');
  });

  req.end();
}

console.log('开始测试API连接...');
testConnection();
