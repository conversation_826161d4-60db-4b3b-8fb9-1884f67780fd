{"name": "student-management-deploy", "version": "1.0.0", "description": "学生管理系统部署包", "scripts": {"deploy": "bash deploy.sh", "start": "pm2 start ecosystem.config.js", "stop": "pm2 stop student-management-api", "restart": "pm2 restart student-management-api", "logs": "pm2 logs student-management-api", "status": "pm2 status"}, "keywords": ["student", "management", "deploy"], "author": "Your Name", "license": "MIT"}