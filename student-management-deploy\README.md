# 学生管理系统部署包

## 部署步骤

1. 将此文件夹上传到服务器 `/home/<USER>/student-management`
2. 进入目录：`cd /home/<USER>/student-management`
3. 给部署脚本执行权限：`chmod +x deploy.sh`
4. 运行部署脚本：`./deploy.sh`

## 常用命令

- 查看应用状态：`pm2 status`
- 查看日志：`pm2 logs student-management-api`
- 重启应用：`pm2 restart student-management-api`
- 停止应用：`pm2 stop student-management-api`

## API 地址

- 服务器地址：http://49.232.148.251
- API 文档：http://49.232.148.251

## 小程序配置

需要在微信小程序后台配置服务器域名：
- request合法域名：49.232.148.251

