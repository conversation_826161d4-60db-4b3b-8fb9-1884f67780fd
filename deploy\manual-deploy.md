# 手动部署步骤

## 1. 创建项目目录
```bash
sudo mkdir -p /home/<USER>/student-management
sudo mkdir -p /home/<USER>/student-management/logs
sudo chown -R ubuntu:ubuntu /home/<USER>/student-management
cd /home/<USER>/student-management
```

## 2. 复制项目文件
将解压后的所有文件复制到 `/home/<USER>/student-management` 目录

## 3. 安装依赖
```bash
cd /home/<USER>/student-management/backend
npm install --production
cd ..
```

## 4. 配置 Nginx
```bash
# 复制 Nginx 配置
sudo cp nginx.conf /etc/nginx/sites-available/student-management

# 启用站点
sudo ln -sf /etc/nginx/sites-available/student-management /etc/nginx/sites-enabled/student-management

# 删除默认站点
sudo rm -f /etc/nginx/sites-enabled/default

# 测试配置
sudo nginx -t

# 重启 Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

## 5. 启动应用
```bash
# 停止可能存在的进程
pm2 stop student-management-api 2>/dev/null || true
pm2 delete student-management-api 2>/dev/null || true

# 启动应用
pm2 start ecosystem.config.js

# 保存配置
pm2 save
pm2 startup
```

## 6. 验证部署
```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs student-management-api

# 测试 API
curl http://localhost:3000
curl http://**************
```

## 常用命令
- 重启应用：`pm2 restart student-management-api`
- 查看日志：`pm2 logs student-management-api`
- 查看 Nginx 状态：`sudo systemctl status nginx`
- 查看 Nginx 日志：`sudo tail -f /var/log/nginx/student_management_access.log`
