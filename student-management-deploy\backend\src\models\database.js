const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class Database {
  constructor() {
    this.db = null;
  }

  // 初始化数据库连接
  init() {
    return new Promise((resolve, reject) => {
      const dbPath = path.join(__dirname, '../../database/students.db');
      this.db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
          console.error('数据库连接失败:', err.message);
          reject(err);
        } else {
          console.log('数据库连接成功');
          this.createTables().then(resolve).catch(reject);
        }
      });
    });
  }

  // 创建学生表
  createTables() {
    return new Promise((resolve, reject) => {
      const createStudentTable = `
        CREATE TABLE IF NOT EXISTS students (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          studentId TEXT UNIQUE NOT NULL,
          class TEXT NOT NULL,
          phone TEXT,
          email TEXT,
          createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
          updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `;

      this.db.run(createStudentTable, (err) => {
        if (err) {
          console.error('创建学生表失败:', err.message);
          reject(err);
        } else {
          console.log('学生表创建成功');
          resolve();
        }
      });
    });
  }

  // 获取所有学生
  getAllStudents() {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM students ORDER BY createdAt DESC';
      this.db.all(sql, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // 根据ID获取学生
  getStudentById(id) {
    return new Promise((resolve, reject) => {
      const sql = 'SELECT * FROM students WHERE id = ?';
      this.db.get(sql, [id], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  // 添加学生
  addStudent(student) {
    return new Promise((resolve, reject) => {
      const { name, studentId, class: className, phone, email } = student;
      const sql = `
        INSERT INTO students (name, studentId, class, phone, email)
        VALUES (?, ?, ?, ?, ?)
      `;
      
      this.db.run(sql, [name, studentId, className, phone, email], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, ...student });
        }
      });
    });
  }

  // 更新学生信息
  updateStudent(id, student) {
    return new Promise((resolve, reject) => {
      const { name, studentId, class: className, phone, email } = student;
      const sql = `
        UPDATE students 
        SET name = ?, studentId = ?, class = ?, phone = ?, email = ?, updatedAt = CURRENT_TIMESTAMP
        WHERE id = ?
      `;
      
      this.db.run(sql, [name, studentId, className, phone, email, id], function(err) {
        if (err) {
          reject(err);
        } else {
          if (this.changes === 0) {
            reject(new Error('学生不存在'));
          } else {
            resolve({ id, ...student });
          }
        }
      });
    });
  }

  // 删除学生
  deleteStudent(id) {
    return new Promise((resolve, reject) => {
      const sql = 'DELETE FROM students WHERE id = ?';
      this.db.run(sql, [id], function(err) {
        if (err) {
          reject(err);
        } else {
          if (this.changes === 0) {
            reject(new Error('学生不存在'));
          } else {
            resolve({ message: '学生删除成功' });
          }
        }
      });
    });
  }

  // 关闭数据库连接
  close() {
    if (this.db) {
      this.db.close((err) => {
        if (err) {
          console.error('关闭数据库连接失败:', err.message);
        } else {
          console.log('数据库连接已关闭');
        }
      });
    }
  }
}

module.exports = new Database();
