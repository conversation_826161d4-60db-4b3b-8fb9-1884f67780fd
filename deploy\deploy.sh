#!/bin/bash

# 学生管理系统部署脚本
echo "🚀 开始部署学生管理系统..."

# 获取当前脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "📍 脚本目录: $SCRIPT_DIR"

# 设置变量
PROJECT_DIR="/home/<USER>/student-management"
BACKUP_DIR="/home/<USER>/backups"
NGINX_CONF="/etc/nginx/sites-available/student-management"
NGINX_ENABLED="/etc/nginx/sites-enabled/student-management"

# 创建必要的目录
echo "📁 创建项目目录..."
sudo mkdir -p $PROJECT_DIR
sudo mkdir -p $BACKUP_DIR
sudo mkdir -p $PROJECT_DIR/logs
sudo chown -R ubuntu:ubuntu $PROJECT_DIR
sudo chown -R ubuntu:ubuntu $BACKUP_DIR

# 复制所有文件到项目目录
echo "📋 复制项目文件..."
cp -r $SCRIPT_DIR/* $PROJECT_DIR/
sudo chown -R ubuntu:ubuntu $PROJECT_DIR

# 进入项目目录
cd $PROJECT_DIR

echo "📦 安装项目依赖..."
# 检查 backend 目录是否存在
if [ -d "backend" ]; then
    cd backend
    npm install --production
    cd ..
else
    echo "❌ backend 目录不存在，请检查项目结构"
    exit 1
fi

echo "🔧 配置 PM2..."
# 检查 ecosystem.config.js 是否存在
if [ ! -f "ecosystem.config.js" ]; then
    echo "❌ ecosystem.config.js 文件不存在"
    exit 1
fi

echo "🌐 配置 Nginx..."
# 检查 nginx.conf 是否存在
if [ ! -f "nginx.conf" ]; then
    echo "❌ nginx.conf 文件不存在"
    exit 1
fi

# 复制 Nginx 配置
sudo cp nginx.conf $NGINX_CONF

# 创建软链接启用站点
sudo ln -sf $NGINX_CONF $NGINX_ENABLED

# 删除默认站点（如果存在）
sudo rm -f /etc/nginx/sites-enabled/default

# 测试 Nginx 配置
echo "🧪 测试 Nginx 配置..."
sudo nginx -t

if [ $? -eq 0 ]; then
    echo "✅ Nginx 配置测试通过"
    
    # 重启 Nginx
    echo "🔄 重启 Nginx..."
    sudo systemctl restart nginx
    sudo systemctl enable nginx
    
    echo "🚀 启动应用..."
    # 停止可能存在的进程
    pm2 stop student-management-api 2>/dev/null || true
    pm2 delete student-management-api 2>/dev/null || true
    
    # 启动应用
    pm2 start ecosystem.config.js
    
    # 保存 PM2 配置
    pm2 save
    pm2 startup
    
    echo "✅ 部署完成！"
    echo "🌐 API 地址: http://49.232.148.251"
    echo "📚 API 文档: http://49.232.148.251"
    echo ""
    echo "📊 查看应用状态: pm2 status"
    echo "📋 查看应用日志: pm2 logs student-management-api"
    echo "🔄 重启应用: pm2 restart student-management-api"
    
else
    echo "❌ Nginx 配置测试失败，请检查配置文件"
    exit 1
fi
